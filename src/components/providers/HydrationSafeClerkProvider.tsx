'use client';

import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { ReactNode, useEffect, useState } from 'react';

interface HydrationSafeClerkProviderProps {
  children: ReactNode;
}

/**
 * A wrapper around ClerkProvider that prevents hydration mismatches
 * by ensuring consistent rendering between server and client
 */
export default function HydrationSafeClerkProvider({ children }: HydrationSafeClerkProviderProps) {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // During SSR and initial client render, show a minimal loading state
  if (!isHydrated) {
    return (
      <div suppressHydrationWarning>
        {children}
      </div>
    );
  }

  // After hydration, render with full Clerk provider
  return (
    <ClerkProvider
      appearance={{
        baseTheme: undefined,
        variables: {
          colorPrimary: '#2563eb',
        },
        elements: {
          formButtonPrimary: 'bg-blue-600 hover:bg-blue-700 text-sm normal-case',
          card: 'shadow-lg',
        },
      }}
    >
      {children}
    </ClerkProvider>
  );
}
